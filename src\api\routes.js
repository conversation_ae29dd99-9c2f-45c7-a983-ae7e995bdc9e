const express = require('express');
const { body, param, validationResult } = require('express-validator');
const AssessmentController = require('../controllers/assessmentController');

const router = express.Router();

/**
 * Middleware to handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Invalid input data provided',
      details: errors.array()
    });
  }
  next();
};

/**
 * Validation rules for RIASEC results
 */
const riasecValidation = body('riasec_results')
  .isObject()
  .withMessage('riasec_results must be an object')
  .custom((value) => {
    const requiredKeys = ['R', 'I', 'A', 'S', 'E', 'C'];
    const providedKeys = Object.keys(value);
    
    // Check if all required keys are present
    for (const key of requiredKeys) {
      if (!providedKeys.includes(key)) {
        throw new Error(`Missing required RIASEC key: ${key}`);
      }
    }
    
    // Check if all values are numbers
    for (const [key, val] of Object.entries(value)) {
      if (typeof val !== 'number' || val < 0) {
        throw new Error(`RIASEC ${key} must be a non-negative number`);
      }
    }
    
    return true;
  });

/**
 * Validation rules for OCEAN results
 */
const oceanValidation = body('ocean_results')
  .isObject()
  .withMessage('ocean_results must be an object')
  .custom((value) => {
    const requiredKeys = ['O', 'C', 'E', 'A', 'N'];
    const providedKeys = Object.keys(value);
    
    // Check if all required keys are present
    for (const key of requiredKeys) {
      if (!providedKeys.includes(key)) {
        throw new Error(`Missing required OCEAN key: ${key}`);
      }
    }
    
    // Check if all values are numbers between 1 and 5
    for (const [key, val] of Object.entries(value)) {
      if (typeof val !== 'number' || val < 1 || val > 5) {
        throw new Error(`OCEAN ${key} must be a number between 1 and 5`);
      }
    }
    
    return true;
  });

/**
 * Validation rules for VIA-IS results
 */
const viaIsValidation = body('via_is_results')
  .isObject()
  .withMessage('via_is_results must be an object')
  .custom((value) => {
    // Check if object has at least some character strengths
    const keys = Object.keys(value);
    if (keys.length === 0) {
      throw new Error('via_is_results must contain at least one character strength');
    }
    
    // Check if all values are numbers
    for (const [key, val] of Object.entries(value)) {
      if (typeof val !== 'number' || val < 0) {
        throw new Error(`VIA-IS ${key} must be a non-negative number`);
      }
    }
    
    return true;
  });

/**
 * Validation rules for Multiple Intelligences results
 */
const multipleIntelligencesValidation = body('multiple_intelligences_results')
  .isObject()
  .withMessage('multiple_intelligences_results must be an object')
  .custom((value) => {
    // Check if object has at least some intelligence types
    const keys = Object.keys(value);
    if (keys.length === 0) {
      throw new Error('multiple_intelligences_results must contain at least one intelligence type');
    }
    
    // Check if all values are numbers
    for (const [key, val] of Object.entries(value)) {
      if (typeof val !== 'number' || val < 0) {
        throw new Error(`Multiple Intelligence ${key} must be a non-negative number`);
      }
    }
    
    return true;
  });

/**
 * Validation rules for Cognitive Style Index results
 */
const cognitiveStyleValidation = body('cognitive_style_index_results')
  .isObject()
  .withMessage('cognitive_style_index_results must be an object')
  .custom((value) => {
    // Check if object has at least some cognitive styles
    const keys = Object.keys(value);
    if (keys.length === 0) {
      throw new Error('cognitive_style_index_results must contain at least one cognitive style');
    }
    
    // Check if all values are numbers
    for (const [key, val] of Object.entries(value)) {
      if (typeof val !== 'number' || val < 0) {
        throw new Error(`Cognitive Style ${key} must be a non-negative number`);
      }
    }
    
    return true;
  });

/**
 * GET /api/assessments
 * List assessments for user
 */
router.get('/api/assessments', AssessmentController.listAssessments);

/**
 * POST /api/assessments
 * Create new assessment (submit assessment for analysis)
 */
router.post('/api/assessments', [
  // Validate all required assessment result objects
  riasecValidation,
  oceanValidation,
  viaIsValidation,
  multipleIntelligencesValidation,
  cognitiveStyleValidation,

  // Handle validation errors
  handleValidationErrors
], AssessmentController.submitAssessment);

/**
 * GET /api/assessments/:id
 * Get assessment details by ID
 */
router.get('/api/assessments/:id', [
  param('id')
    .isUUID()
    .withMessage('Assessment ID must be a valid UUID'),

  handleValidationErrors
], AssessmentController.getAssessmentDetails);

/**
 * POST /api/assessments/:id/submit
 * Submit answers for assessment (alternative endpoint)
 */
router.post('/api/assessments/:id/submit', [
  param('id')
    .isUUID()
    .withMessage('Assessment ID must be a valid UUID'),

  // Validate all required assessment result objects
  riasecValidation,
  oceanValidation,
  viaIsValidation,
  multipleIntelligencesValidation,
  cognitiveStyleValidation,

  handleValidationErrors
], AssessmentController.submitAssessmentById);

/**
 * GET /api/assessments/:id/results
 * Get assessment results by ID
 */
router.get('/api/assessments/:id/results', [
  param('id')
    .isUUID()
    .withMessage('Assessment ID must be a valid UUID'),

  handleValidationErrors
], AssessmentController.getAssessmentResults);

/**
 * Legacy endpoints for backward compatibility
 */

/**
 * POST /submit
 * Submit assessment for analysis (legacy)
 */
router.post('/submit', [
  // Validate all required assessment result objects
  riasecValidation,
  oceanValidation,
  viaIsValidation,
  multipleIntelligencesValidation,
  cognitiveStyleValidation,

  // Handle validation errors
  handleValidationErrors
], AssessmentController.submitAssessment);

/**
 * GET /status/:profileId
 * Get assessment status by profile ID (legacy)
 */
router.get('/status/:profileId', [
  param('profileId')
    .isUUID()
    .withMessage('Profile ID must be a valid UUID'),

  handleValidationErrors
], AssessmentController.getAssessmentStatus);

/**
 * Health check endpoint
 * GET /health
 */
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'assessment-service',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
