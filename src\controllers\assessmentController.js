const { v4: uuidv4 } = require('uuid');
const { transaction } = require('../config/db');
const QueueService = require('../services/queueService');
const ArchiveService = require('../services/archiveService');

/**
 * Assessment Controller
 * Handles the core business logic for assessment submission
 */
class AssessmentController {
  
  /**
   * Submit assessment for analysis
   * POST /submit
   */
  static async submitAssessment(req, res) {
    try {
      // Extract user ID from headers (set by API Gateway) or auth object
      const userId = req.currentUser?.id || req.auth?.id || req.user?.id;

      if (!userId) {
        return res.status(401).json({
          error: 'User authentication required',
          message: 'User ID not found in request'
        });
      }
      
      // Extract assessment data from request body
      const {
        riasec_results,
        ocean_results,
        via_is_results,
        multiple_intelligences_results,
        cognitive_style_index_results
      } = req.body;

      console.log('Processing assessment submission for user:', userId);

      // Transform data to format expected by analysis worker
      const transformedData = AssessmentController.transformAssessmentData({
        riasec_results,
        ocean_results,
        via_is_results,
        multiple_intelligences_results,
        cognitive_style_index_results
      });
      
      // Step 1: Create profile in Archive Service first
      let profile;
      try {
        console.log('Creating profile in Archive Service...');
        profile = await ArchiveService.createProfile(userId, transformedData);
        console.log('Profile created in Archive Service:', profile.id);
      } catch (archiveError) {
        console.error('Failed to create profile in Archive Service:', archiveError);
        return res.status(500).json({
          error: 'Failed to create profile',
          message: 'Could not create profile in Archive Service'
        });
      }

      // Step 2: Execute quota check and reduction in database transaction
      let result;

      if (process.env.NODE_ENV === 'test' || process.env.DB_HOST === 'localhost') {
        // Mock database operations for testing
        console.log('Database operations mocked for testing');
        console.log(`Mock: Checking quota for user ${userId}`);
        console.log(`Mock: Quota available, reducing by 1`);

        result = {
          profileId: profile.id,
          createdAt: profile.created_at
        };
      } else {
        // Real database operations - only quota management
        result = await transaction(async (client) => {

          // Check and reduce user quota with row locking
          const quotaQuery = `
            SELECT analysis_quota
            FROM admin_schema.user_plans
            WHERE user_id = $1
            FOR UPDATE
          `;

          const quotaResult = await client.query(quotaQuery, [userId]);

          if (quotaResult.rows.length === 0) {
            throw new Error('User plan not found');
          }

          const currentQuota = quotaResult.rows[0].analysis_quota;

          if (currentQuota <= 0) {
            const error = new Error('Insufficient analysis quota');
            error.statusCode = 403;
            throw error;
          }

          // Reduce quota by 1
          const updateQuotaQuery = `
            UPDATE admin_schema.user_plans
            SET analysis_quota = analysis_quota - 1
            WHERE user_id = $1
          `;

          await client.query(updateQuotaQuery, [userId]);

          console.log(`Quota reduced for user ${userId}. Remaining: ${currentQuota - 1}`);

          // Log submission (optional)
          try {
            const logQuery = `
              INSERT INTO assessment_log_schema.submission_logs
              (profile_id, user_id, submission_data, status, created_at)
              VALUES ($1, $2, $3, $4, NOW())
            `;

            await client.query(logQuery, [
              profile.id,
              userId,
              JSON.stringify(transformedData),
              'submitted'
            ]);
          } catch (logError) {
            // Log error but don't fail the transaction
            console.warn('Failed to log submission:', logError.message);
          }

          return {
            profileId: profile.id,
            createdAt: profile.created_at
          };
        });
      }
      
      // Step 3: Publish to RabbitMQ (outside transaction)
      try {
        await QueueService.publishAssessmentJob(result.profileId, userId);

        console.log('Assessment job queued successfully:', result.profileId);

      } catch (queueError) {
        console.error('Failed to queue assessment job:', queueError.message);

        // Update profile status in Archive Service to indicate queue failure
        try {
          await ArchiveService.updateProfileStatus(result.profileId, 'queue_failed');
        } catch (updateError) {
          console.error('Failed to update profile status in Archive Service:', updateError.message);
        }

        // Return error to client
        return res.status(500).json({
          error: 'Failed to queue assessment for analysis',
          message: 'Assessment was saved but could not be queued for processing',
          profileId: result.profileId
        });
      }
      
      // Step 4: Return success response
      res.status(202).json({
        message: 'Assessment queued successfully',
        profileId: result.profileId,
        status: 'pending'
      });
      
    } catch (error) {
      console.error('Assessment submission error:', {
        userId: req.auth?.id || req.user?.id,
        error: error.message,
        stack: error.stack
      });
      
      // Handle specific error types
      if (error.statusCode === 403) {
        return res.status(403).json({
          error: 'Quota exceeded',
          message: 'You have insufficient analysis quota to perform this assessment'
        });
      }
      
      if (error.message.includes('User plan not found')) {
        return res.status(404).json({
          error: 'User plan not found',
          message: 'No valid subscription plan found for this user'
        });
      }
      
      // Generic server error
      res.status(500).json({
        error: 'Internal server error',
        message: 'An error occurred while processing your assessment'
      });
    }
  }
  
  /**
   * Get assessment status
   * GET /status/:profileId
   */
  static async getAssessmentStatus(req, res) {
    try {
      const { profileId } = req.params;
      const userId = req.currentUser?.id || req.auth?.id || req.user?.id;

      if (!userId) {
        return res.status(401).json({
          error: 'User authentication required'
        });
      }
      
      if (process.env.NODE_ENV === 'test' || process.env.DB_HOST === 'localhost') {
        // Mock response for testing
        console.log(`Mock: Getting status for profile ${profileId}`);
        res.json({
          profileId: profileId,
          status: 'processing',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      } else {
        // Real database query
        const query = `
          SELECT id, status, created_at, updated_at
          FROM archive_schema.persona_profiles
          WHERE id = $1 AND user_id = $2
        `;

        const result = await require('../config/db').query(query, [profileId, userId]);

        if (result.rows.length === 0) {
          return res.status(404).json({
            error: 'Assessment not found',
            message: 'No assessment found with the provided ID'
          });
        }

        const profile = result.rows[0];

        res.json({
          profileId: profile.id,
          status: profile.status,
          createdAt: profile.created_at,
          updatedAt: profile.updated_at
        });
      }
      
    } catch (error) {
      console.error('Get assessment status error:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to retrieve assessment status'
      });
    }
  }

  /**
   * List assessments for user
   * GET /api/assessments
   */
  static async listAssessments(req, res) {
    try {
      const userId = req.currentUser?.id || req.auth?.id || req.user?.id;

      if (!userId) {
        return res.status(401).json({
          error: 'User authentication required'
        });
      }

      if (process.env.NODE_ENV === 'test' || process.env.DB_HOST === 'localhost') {
        // Mock response for testing
        console.log(`Mock: Getting assessments list for user ${userId}`);
        res.json({
          assessments: [
            {
              id: 'mock-profile-1',
              status: 'completed',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            {
              id: 'mock-profile-2',
              status: 'processing',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          ],
          total: 2
        });
      } else {
        // Real database query
        const query = `
          SELECT id, status, created_at, updated_at
          FROM archive_schema.persona_profiles
          WHERE user_id = $1
          ORDER BY created_at DESC
        `;

        const result = await require('../config/db').query(query, [userId]);

        res.json({
          assessments: result.rows.map(row => ({
            id: row.id,
            status: row.status,
            createdAt: row.created_at,
            updatedAt: row.updated_at
          })),
          total: result.rows.length
        });
      }

    } catch (error) {
      console.error('List assessments error:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to retrieve assessments list'
      });
    }
  }

  /**
   * Get assessment details by ID
   * GET /api/assessments/:id
   */
  static async getAssessmentDetails(req, res) {
    try {
      const { id } = req.params;
      const userId = req.currentUser?.id || req.auth?.id || req.user?.id;

      if (!userId) {
        return res.status(401).json({
          error: 'User authentication required'
        });
      }

      if (process.env.NODE_ENV === 'test' || process.env.DB_HOST === 'localhost') {
        // Mock response for testing
        console.log(`Mock: Getting assessment details for ${id}`);
        res.json({
          id: id,
          status: 'completed',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          rawInputData: {
            riasec_results: { R: 85, I: 92, A: 45, S: 78, E: 67, C: 55 },
            ocean_results: { O: 4.2, C: 4.8, E: 3.5, A: 4.1, N: 2.3 }
          }
        });
      } else {
        // Real database query
        const query = `
          SELECT id, status, raw_input_data, created_at, updated_at
          FROM archive_schema.persona_profiles
          WHERE id = $1 AND user_id = $2
        `;

        const result = await require('../config/db').query(query, [id, userId]);

        if (result.rows.length === 0) {
          return res.status(404).json({
            error: 'Assessment not found',
            message: 'No assessment found with the provided ID'
          });
        }

        const profile = result.rows[0];

        res.json({
          id: profile.id,
          status: profile.status,
          createdAt: profile.created_at,
          updatedAt: profile.updated_at,
          rawInputData: profile.raw_input_data
        });
      }

    } catch (error) {
      console.error('Get assessment details error:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to retrieve assessment details'
      });
    }
  }

  /**
   * Submit answers for assessment by ID
   * POST /api/assessments/:id/submit
   */
  static async submitAssessmentById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.currentUser?.id || req.auth?.id || req.user?.id;

      if (!userId) {
        return res.status(401).json({
          error: 'User authentication required'
        });
      }

      // For now, this endpoint behaves the same as regular submit
      // In a more complex system, this might update an existing assessment
      console.log(`Submitting assessment answers for assessment ID: ${id}`);

      // Call the regular submit method
      return AssessmentController.submitAssessment(req, res);

    } catch (error) {
      console.error('Submit assessment by ID error:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to submit assessment answers'
      });
    }
  }

  /**
   * Get assessment results by ID
   * GET /api/assessments/:id/results
   */
  static async getAssessmentResults(req, res) {
    try {
      const { id } = req.params;
      const userId = req.currentUser?.id || req.auth?.id || req.user?.id;

      if (!userId) {
        return res.status(401).json({
          error: 'User authentication required'
        });
      }

      if (process.env.NODE_ENV === 'test' || process.env.DB_HOST === 'localhost') {
        // Mock response for testing
        console.log(`Mock: Getting assessment results for ${id}`);
        res.json({
          id: id,
          status: 'completed',
          results: {
            persona_result: {
              personality_type: 'INVESTIGATIVE_RESEARCHER',
              description: 'You are naturally curious and analytical...',
              strengths: ['Problem-solving', 'Research', 'Analysis'],
              career_suggestions: ['Data Scientist', 'Research Scientist', 'Software Engineer']
            }
          },
          createdAt: new Date().toISOString(),
          completedAt: new Date().toISOString()
        });
      } else {
        // Real database query
        const query = `
          SELECT id, status, persona_result, created_at, updated_at
          FROM archive_schema.persona_profiles
          WHERE id = $1 AND user_id = $2
        `;

        const result = await require('../config/db').query(query, [id, userId]);

        if (result.rows.length === 0) {
          return res.status(404).json({
            error: 'Assessment not found',
            message: 'No assessment found with the provided ID'
          });
        }

        const profile = result.rows[0];

        if (profile.status !== 'completed') {
          return res.status(202).json({
            id: profile.id,
            status: profile.status,
            message: 'Assessment is still being processed',
            createdAt: profile.created_at,
            updatedAt: profile.updated_at
          });
        }

        res.json({
          id: profile.id,
          status: profile.status,
          results: profile.persona_result,
          createdAt: profile.created_at,
          completedAt: profile.updated_at
        });
      }

    } catch (error) {
      console.error('Get assessment results error:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to retrieve assessment results'
      });
    }
  }

  /**
   * Transform assessment data to format expected by analysis worker
   * @param {Object} assessmentData - Raw assessment data from frontend
   * @returns {Object} - Transformed data in RIASEC/OCEAN format
   */
  static transformAssessmentData(assessmentData) {
    const {
      riasec_results,
      ocean_results,
      via_is_results,
      multiple_intelligences_results,
      cognitive_style_index_results
    } = assessmentData;

    // Transform RIASEC data to expected format
    const riasec = {
      realistic: riasec_results.R || 0,
      investigative: riasec_results.I || 0,
      artistic: riasec_results.A || 0,
      social: riasec_results.S || 0,
      enterprising: riasec_results.E || 0,
      conventional: riasec_results.C || 0
    };

    // Transform OCEAN data to expected format
    const ocean = {
      openness: ocean_results.O || 0,
      conscientiousness: ocean_results.C || 0,
      extraversion: ocean_results.E || 0,
      agreeableness: ocean_results.A || 0,
      neuroticism: ocean_results.N || 0
    };

    // Return transformed data in the format expected by analysis worker
    return {
      riasec,
      ocean,
      // Keep original data for reference
      original_data: {
        riasec_results,
        ocean_results,
        via_is_results,
        multiple_intelligences_results,
        cognitive_style_index_results
      }
    };
  }
}

module.exports = AssessmentController;
