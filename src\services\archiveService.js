/**
 * Archive Service Client
 * Handles communication with the Archive Service for profile management
 */
class ArchiveService {
  
  constructor() {
    this.baseUrl = process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002';
    this.timeout = 10000; // 10 seconds timeout
  }

  /**
   * Create a new profile in Archive Service
   * @param {string} userId - User ID
   * @param {Object} rawInputData - Assessment data in RIASEC/OCEAN format
   * @returns {Promise<Object>} - Created profile data
   */
  async createProfile(userId, rawInputData) {
    try {
      // For testing/development, mock the Archive Service call
      if (process.env.NODE_ENV === 'test' || process.env.ARCHIVE_SERVICE_URL === 'http://localhost:3002') {
        console.log('Archive Service call mocked for testing');
        const mockProfileId = require('uuid').v4();
        
        console.log(`Mock: Creating profile for user ${userId}`);
        console.log('Mock: Profile data:', JSON.stringify(rawInputData, null, 2));
        
        return {
          id: mockProfileId,
          userId: userId,
          status: 'pending',
          raw_input_data: rawInputData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }

      // Real HTTP call to Archive Service
      const response = await this.makeHttpRequest('POST', '/profiles', {
        userId: userId,
        raw_input_data: rawInputData,
        status: 'pending'
      });

      console.log('Profile created in Archive Service:', response.id);
      return response;

    } catch (error) {
      console.error('Failed to create profile in Archive Service:', error);
      throw new Error(`Archive Service error: ${error.message}`);
    }
  }

  /**
   * Get profile by ID from Archive Service
   * @param {string} profileId - Profile ID
   * @returns {Promise<Object>} - Profile data
   */
  async getProfile(profileId) {
    try {
      // For testing/development, mock the Archive Service call
      if (process.env.NODE_ENV === 'test' || process.env.ARCHIVE_SERVICE_URL === 'http://localhost:3002') {
        console.log(`Mock: Getting profile ${profileId} from Archive Service`);
        
        return {
          id: profileId,
          userId: 'test-user-id',
          status: 'processing',
          raw_input_data: {
            riasec: { realistic: 85, investigative: 92, artistic: 45, social: 78, enterprising: 67, conventional: 55 },
            ocean: { openness: 78, conscientiousness: 88, extraversion: 65, agreeableness: 82, neuroticism: 35 }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }

      // Real HTTP call to Archive Service
      const response = await this.makeHttpRequest('GET', `/profiles/${profileId}`);
      return response;

    } catch (error) {
      console.error('Failed to get profile from Archive Service:', error);
      throw new Error(`Archive Service error: ${error.message}`);
    }
  }

  /**
   * Update profile status in Archive Service
   * @param {string} profileId - Profile ID
   * @param {string} status - New status
   * @returns {Promise<Object>} - Updated profile data
   */
  async updateProfileStatus(profileId, status) {
    try {
      // For testing/development, mock the Archive Service call
      if (process.env.NODE_ENV === 'test' || process.env.ARCHIVE_SERVICE_URL === 'http://localhost:3002') {
        console.log(`Mock: Updating profile ${profileId} status to ${status}`);
        
        return {
          id: profileId,
          status: status,
          updated_at: new Date().toISOString()
        };
      }

      // Real HTTP call to Archive Service
      const response = await this.makeHttpRequest('PATCH', `/profiles/${profileId}`, {
        status: status
      });

      console.log(`Profile ${profileId} status updated to ${status}`);
      return response;

    } catch (error) {
      console.error('Failed to update profile status in Archive Service:', error);
      throw new Error(`Archive Service error: ${error.message}`);
    }
  }

  /**
   * Make HTTP request to Archive Service
   * @param {string} method - HTTP method
   * @param {string} path - API path
   * @param {Object} data - Request body data
   * @returns {Promise<Object>} - Response data
   */
  async makeHttpRequest(method, path, data = null) {
    // For now, we'll use a simple fetch-like implementation
    // In production, you might want to use axios or another HTTP client
    
    const url = `${this.baseUrl}${path}`;
    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'assessment-service/1.0.0'
      },
      timeout: this.timeout
    };

    if (data && (method === 'POST' || method === 'PATCH' || method === 'PUT')) {
      options.body = JSON.stringify(data);
    }

    try {
      // Since Node.js doesn't have fetch built-in by default, we'll simulate it
      // In a real implementation, you would use fetch, axios, or the built-in http module
      
      console.log(`Making ${method} request to ${url}`);
      if (data) {
        console.log('Request data:', JSON.stringify(data, null, 2));
      }

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 100));

      // For now, return a mock response
      // In production, replace this with actual HTTP client implementation
      throw new Error('HTTP client not implemented - using mock responses');

    } catch (error) {
      console.error(`HTTP request failed: ${method} ${url}`, error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new ArchiveService();
