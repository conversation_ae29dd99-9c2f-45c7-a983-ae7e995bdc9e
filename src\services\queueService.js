const { publishMessage } = require('../config/rabbitmq');

/**
 * Queue service for handling assessment job publishing to RabbitMQ
 */
class QueueService {
  
  /**
   * Publish an assessment job to the analysis queue
   * @param {string} profileId - The unique profile ID for the assessment
   * @param {string} userId - The user ID who submitted the assessment
   * @returns {Promise<boolean>} - Returns true if successfully published
   */
  static async publishAssessmentJob(profileId, userId) {
    try {
      // Validate required parameters
      if (!profileId) {
        throw new Error('Profile ID is required for publishing assessment job');
      }

      if (!userId) {
        throw new Error('User ID is required for publishing assessment job');
      }

      // Create job message in the exact format expected by analysis worker
      const jobMessage = {
        profileId,
        userId
      };

      console.log('Publishing assessment job to queue:', {
        profileId,
        userId
      });

      // Publish message to RabbitMQ
      await publishMessage(jobMessage);

      console.log('Assessment job published successfully:', profileId);
      return true;

    } catch (error) {
      console.error('Failed to publish assessment job:', {
        profileId,
        userId,
        error: error.message,
        stack: error.stack
      });

      // Re-throw error to be handled by the controller
      throw new Error(`Failed to queue assessment job: ${error.message}`);
    }
  }
  
  /**
   * Publish a high-priority assessment job
   * @param {string} profileId - The unique profile ID for the assessment
   * @param {string} userId - The user ID who submitted the assessment
   * @returns {Promise<boolean>} - Returns true if successfully published
   */
  static async publishPriorityAssessmentJob(profileId, userId) {
    // For now, priority jobs use the same format as regular jobs
    // The analysis worker can implement priority handling based on queue configuration
    return this.publishAssessmentJob(profileId, userId);
  }

  /**
   * Publish a retry assessment job (for failed analyses)
   * @param {string} profileId - The unique profile ID for the assessment
   * @param {string} userId - The user ID who submitted the assessment
   * @returns {Promise<boolean>} - Returns true if successfully published
   */
  static async publishRetryAssessmentJob(profileId, userId) {
    // For retry jobs, we use the same simple format
    // The analysis worker can track retry attempts internally
    return this.publishAssessmentJob(profileId, userId);
  }
  
  /**
   * Validate job data before publishing
   * @param {Object} jobData - The job data to validate
   * @returns {boolean} - Returns true if valid
   * @throws {Error} - Throws error if validation fails
   */
  static validateJobData(jobData) {
    const requiredFields = ['profileId', 'userId'];
    
    for (const field of requiredFields) {
      if (!jobData[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    // Validate profileId format (should be UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(jobData.profileId)) {
      throw new Error('Invalid profileId format. Must be a valid UUID.');
    }
    
    return true;
  }
}

module.exports = QueueService;
