#!/usr/bin/env node

/**
 * Test script for API Gateway integration
 * Tests the new /api/assessments/* endpoints with proper headers
 */

const http = require('http');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_USER_ID = 'test-user-123';
const TEST_USER_EMAIL = '<EMAIL>';

// Sample assessment data
const sampleAssessmentData = {
  riasec_results: {
    R: 85,
    I: 92,
    A: 45,
    S: 78,
    E: 67,
    C: 55
  },
  ocean_results: {
    O: 4.2,
    C: 4.8,
    E: 3.5,
    A: 4.1,
    N: 2.3
  },
  via_is_results: {
    creativity: 85,
    curiosity: 90,
    judgment: 88,
    love_of_learning: 92,
    perspective: 78
  },
  multiple_intelligences_results: {
    linguistic: 75,
    logical_mathematical: 95,
    spatial: 60,
    musical: 40,
    bodily_kinesthetic: 55,
    interpersonal: 80,
    intrapersonal: 85,
    naturalistic: 70
  },
  cognitive_style_index_results: {
    analytical: 90,
    creative: 75,
    practical: 65
  }
};

/**
 * Make HTTP request with proper headers
 */
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': TEST_USER_ID,
        'X-User-Email': TEST_USER_EMAIL,
        'Authorization': 'Bearer test-token'
      }
    };

    if (data) {
      const jsonData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(jsonData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * Run tests
 */
async function runTests() {
  console.log('🧪 Testing API Gateway Integration');
  console.log('=' .repeat(50));
  
  try {
    // Test 1: Health check
    console.log('\n1. Testing health endpoint...');
    const healthResponse = await makeRequest('GET', '/health');
    console.log(`   Status: ${healthResponse.statusCode}`);
    console.log(`   Response:`, healthResponse.data);
    
    // Test 2: List assessments
    console.log('\n2. Testing GET /api/assessments...');
    const listResponse = await makeRequest('GET', '/api/assessments');
    console.log(`   Status: ${listResponse.statusCode}`);
    console.log(`   Response:`, listResponse.data);
    
    // Test 3: Submit new assessment
    console.log('\n3. Testing POST /api/assessments...');
    const submitResponse = await makeRequest('POST', '/api/assessments', sampleAssessmentData);
    console.log(`   Status: ${submitResponse.statusCode}`);
    console.log(`   Response:`, submitResponse.data);
    
    let profileId = null;
    if (submitResponse.data && submitResponse.data.profileId) {
      profileId = submitResponse.data.profileId;
      
      // Test 4: Get assessment details
      console.log('\n4. Testing GET /api/assessments/:id...');
      const detailsResponse = await makeRequest('GET', `/api/assessments/${profileId}`);
      console.log(`   Status: ${detailsResponse.statusCode}`);
      console.log(`   Response:`, detailsResponse.data);
      
      // Test 5: Get assessment results
      console.log('\n5. Testing GET /api/assessments/:id/results...');
      const resultsResponse = await makeRequest('GET', `/api/assessments/${profileId}/results`);
      console.log(`   Status: ${resultsResponse.statusCode}`);
      console.log(`   Response:`, resultsResponse.data);
    }
    
    // Test 6: Test without authentication headers
    console.log('\n6. Testing authentication requirement...');
    const noAuthResponse = await makeRequest('GET', '/api/assessments').catch(() => null);
    if (noAuthResponse) {
      // Make request without headers
      const url = new URL('/api/assessments', BASE_URL);
      const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
          // No X-User-ID or X-User-Email headers
        }
      };
      
      const req = http.request(options, (res) => {
        let responseData = '';
        res.on('data', (chunk) => {
          responseData += chunk;
        });
        res.on('end', () => {
          try {
            const parsedData = JSON.parse(responseData);
            console.log(`   Status: ${res.statusCode}`);
            console.log(`   Response:`, parsedData);
          } catch (error) {
            console.log(`   Status: ${res.statusCode}`);
            console.log(`   Response:`, responseData);
          }
        });
      });
      
      req.on('error', (error) => {
        console.log(`   Error: ${error.message}`);
      });
      
      req.end();
    }
    
    console.log('\n✅ All tests completed!');
    console.log('=' .repeat(50));
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the tests
runTests();
